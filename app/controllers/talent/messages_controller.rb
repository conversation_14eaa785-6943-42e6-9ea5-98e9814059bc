module Talent
  class MessagesController < Talent::BaseController
    before_action :set_conversation, except: [:index]

    def index
      # Show all messages for the current user across all conversations
      @conversations = Current.user.conversations
                                  .includes(:users, :job, messages: :user)
                                  .joins(:messages)
                                  .order('messages.created_at DESC')
                                  .distinct

      # Get recent messages for display
      @recent_messages = Message.joins(:conversation)
                               .joins('JOIN conversation_participants ON conversation_participants.conversation_id = conversations.id')
                               .where('conversation_participants.user_id = ?', Current.user.id)
                               .includes(:user, conversation: [:users, :job])
                               .order(created_at: :desc)
                               .limit(50)
    end

    def create
      @message = @conversation.messages.new(message_params)
      @message.user = Current.user

      if @message.save
        respond_to do |format|
          format.turbo_stream do
            render turbo_stream: [
              turbo_stream.append("messages", partial: "talent/conversations/message", locals: { message: @message }),
              turbo_stream.replace("new_message_form", partial: "talent/conversations/form", locals: { conversation: @conversation, message: Message.new })
            ]
          end
        end
      else
        # Handle validation errors (if any)
        # This might involve re-rendering the form with errors
        render turbo_stream: turbo_stream.replace("new_message_form", partial: "talent/conversations/form", locals: { conversation: @conversation, message: @message })
      end
    end

    private

    def set_conversation
      @conversation = Current.user.conversations.find(params[:conversation_id])
    rescue ActiveRecord::RecordNotFound
      redirect_to talent_conversations_path, alert: "Conversation not found."
    end

    def message_params
      params.require(:message).permit(:body)
    end
  end
end
