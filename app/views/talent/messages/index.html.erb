<% content_for :title, "Messages" %>

<div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
  <!-- Header -->
  <div class="mb-8">
    <h1 class="text-3xl font-bold text-stone-900">Messages</h1>
    <p class="mt-2 text-stone-600">View all your messages across conversations</p>
  </div>

  <!-- Messages List -->
  <div class="bg-white shadow-sm rounded-lg border border-stone-200">
    <% if @recent_messages.any? %>
      <div class="divide-y divide-stone-200">
        <% @recent_messages.each do |message| %>
          <div class="p-6 hover:bg-stone-50 transition duration-150 ease-in-out">
            <div class="flex items-start space-x-4">
              <!-- Avatar -->
              <div class="flex-shrink-0">
                <div class="w-10 h-10 rounded-full bg-stone-200 flex items-center justify-center">
                  <span class="text-sm font-medium text-stone-600">
                    <%= message.user.first_name.first.upcase %>
                  </span>
                </div>
              </div>
              
              <!-- Message Content -->
              <div class="flex-1 min-w-0">
                <div class="flex items-center justify-between">
                  <div class="flex items-center space-x-2">
                    <p class="text-sm font-medium text-stone-900">
                      <%= message.user.name.full %>
                    </p>
                    <% if message.conversation.job %>
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        <%= message.conversation.job.title %>
                      </span>
                    <% end %>
                  </div>
                  <p class="text-sm text-stone-500">
                    <%= time_ago_in_words(message.created_at) %> ago
                  </p>
                </div>
                
                <p class="mt-1 text-sm text-stone-600 line-clamp-2">
                  <%= message.body %>
                </p>
                
                <!-- Conversation Link -->
                <div class="mt-2">
                  <%= link_to "View Conversation", 
                      talent_conversation_path(message.conversation),
                      class: "text-sm text-blue-600 hover:text-blue-500 font-medium" %>
                </div>
              </div>
            </div>
          </div>
        <% end %>
      </div>
    <% else %>
      <!-- Empty State -->
      <div class="text-center py-12">
        <div class="mx-auto w-24 h-24 rounded-full bg-stone-100 flex items-center justify-center mb-4">
          <svg class="w-12 h-12 text-stone-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
          </svg>
        </div>
        <h3 class="text-lg font-medium text-stone-900 mb-2">No messages yet</h3>
        <p class="text-stone-500 mb-6">Start a conversation with a scout to see messages here.</p>
        <%= link_to "Browse Jobs", 
            talent_jobs_path,
            class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" %>
      </div>
    <% end %>
  </div>
</div>
